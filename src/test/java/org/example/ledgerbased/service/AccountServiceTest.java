package org.example.ledgerbased.service;

import org.example.ledgerbased.exception.AccountException;
import org.example.ledgerbased.model.Account;
import org.example.ledgerbased.model.AccountType;
import org.example.ledgerbased.repository.AccountRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AccountServiceTest {

    @Mock
    private AccountRepository accountRepository;

    @InjectMocks
    private AccountService accountService;

    private Account testAccount;

    @BeforeEach
    void setUp() {
        testAccount = new Account("ACC-001", "Test Account", AccountType.ASSET, 1L, BigDecimal.valueOf(1000), 1L);
        testAccount.setId(1L);
    }

    @Test
    void createAccount_Success() {
        // Given
        when(accountRepository.existsByAccountNumber(anyString())).thenReturn(false);
        when(accountRepository.save(any(Account.class))).thenReturn(testAccount);

        // When
        Account result = accountService.createAccount("ACC-001", "Test Account", AccountType.ASSET, 1L, BigDecimal.valueOf(1000), 1L);

        // Then
        assertNotNull(result);
        assertEquals("ACC-001", result.getAccountNumber());
        assertEquals("Test Account", result.getAccountName());
        assertEquals(AccountType.ASSET, result.getAccountType());
        verify(accountRepository).save(any(Account.class));
    }

    @Test
    void createAccount_DuplicateAccountNumber_ThrowsException() {
        // Given
        when(accountRepository.existsByAccountNumber("ACC-001")).thenReturn(true);

        // When & Then
        assertThrows(AccountException.DuplicateAccountNumberException.class, () -> {
            accountService.createAccount("ACC-001", "Test Account", AccountType.ASSET, 1L, BigDecimal.valueOf(1000), 1L);
        });

        verify(accountRepository, never()).save(any(Account.class));
    }

    @Test
    void getAccountById_Success() {
        // Given
        when(accountRepository.findById(1L)).thenReturn(Optional.of(testAccount));

        // When
        Account result = accountService.getAccountById(1L);

        // Then
        assertNotNull(result);
        assertEquals(testAccount.getId(), result.getId());
        assertEquals(testAccount.getAccountNumber(), result.getAccountNumber());
    }

    @Test
    void getAccountById_NotFound_ThrowsException() {
        // Given
        when(accountRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(AccountException.AccountNotFoundException.class, () -> {
            accountService.getAccountById(1L);
        });
    }

    @Test
    void getAccountByNumber_Success() {
        // Given
        when(accountRepository.findByAccountNumber("ACC-001")).thenReturn(Optional.of(testAccount));

        // When
        Account result = accountService.getAccountByNumber("ACC-001");

        // Then
        assertNotNull(result);
        assertEquals("ACC-001", result.getAccountNumber());
    }

    @Test
    void getAccountByNumber_NotFound_ThrowsException() {
        // Given
        when(accountRepository.findByAccountNumber("ACC-001")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(AccountException.AccountNotFoundException.class, () -> {
            accountService.getAccountByNumber("ACC-001");
        });
    }

    @Test
    void deactivateAccount_Success() {
        // Given
        when(accountRepository.findById(1L)).thenReturn(Optional.of(testAccount));
        when(accountRepository.countSubAccounts(1L)).thenReturn(0L);
        when(accountRepository.save(any(Account.class))).thenReturn(testAccount);

        // When
        accountService.deactivateAccount(1L);

        // Then
        verify(accountRepository).save(any(Account.class));
    }

    @Test
    void deactivateAccount_WithSubAccounts_ThrowsException() {
        // Given
        when(accountRepository.findById(1L)).thenReturn(Optional.of(testAccount));
        when(accountRepository.countSubAccounts(1L)).thenReturn(2L);

        // When & Then
        assertThrows(AccountException.class, () -> {
            accountService.deactivateAccount(1L);
        });

        verify(accountRepository, never()).save(any(Account.class));
    }
}
