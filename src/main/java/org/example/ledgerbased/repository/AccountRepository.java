package org.example.ledgerbased.repository;

import org.example.ledgerbased.model.Account;
import org.example.ledgerbased.model.AccountType;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AccountRepository extends CrudRepository<Account, Long> {

    Optional<Account> findByAccountNumber(String accountNumber);

    List<Account> findByAccountType(AccountType accountType);

    List<Account> findByParentAccountId(Long parentAccountId);

    List<Account> findByIsActiveTrue();

    @Query("SELECT * FROM accounts WHERE account_type = :accountType AND is_active = true")
    List<Account> findActiveAccountsByType(@Param("accountType") AccountType accountType);

    @Query("SELECT * FROM accounts WHERE parent_account_id IS NULL AND is_active = true")
    List<Account> findRootAccounts();

    @Query("SELECT * FROM accounts WHERE parent_account_id = :parentId AND is_active = true")
    List<Account> findActiveSubAccounts(@Param("parentId") Long parentId);

    @Query("SELECT * FROM accounts WHERE currency_id = :currencyId AND is_active = true")
    List<Account> findAccountsByCurrency(@Param("currencyId") Long currencyId);

    @Query("SELECT * FROM accounts WHERE (account_number ILIKE :searchTerm OR account_name ILIKE :searchTerm) AND is_active = true")
    List<Account> searchActiveAccounts(@Param("searchTerm") String searchTerm);

    @Query("SELECT SUM(current_balance) FROM accounts WHERE account_type = :accountType AND is_active = true")
    java.math.BigDecimal getTotalBalanceByAccountType(@Param("accountType") AccountType accountType);

    @Query("SELECT * FROM accounts WHERE account_type IN (:types) AND is_active = true ORDER BY account_number")
    List<Account> findAccountsByTypes(@Param("types") List<AccountType> types);

    boolean existsByAccountNumber(String accountNumber);

    @Query("SELECT COUNT(*) FROM accounts WHERE parent_account_id = :parentId AND is_active = true")
    long countSubAccounts(@Param("parentId") Long parentId);
}
