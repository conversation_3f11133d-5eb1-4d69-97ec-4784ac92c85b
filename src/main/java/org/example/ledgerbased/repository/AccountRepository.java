package org.example.ledgerbased.repository;

import org.example.ledgerbased.model.Account;
import org.example.ledgerbased.model.AccountType;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 账户数据访问接口
 * 提供账户相关的数据库操作方法，包括基本的CRUD操作和复杂查询
 * 支持按类型、层级、状态等多种条件查询账户信息
 */
@Repository
public interface AccountRepository extends CrudRepository<Account, Long> {

    /**
     * 根据账户编号查找账户
     *
     * @param accountNumber 账户编号
     * @return 账户对象的Optional包装
     */
    Optional<Account> findByAccountNumber(String accountNumber);

    /**
     * 根据账户类型查找所有账户
     *
     * @param accountType 账户类型
     * @return 指定类型的账户列表
     */
    List<Account> findByAccountType(AccountType accountType);

    /**
     * 根据父账户ID查找所有子账户
     *
     * @param parentAccountId 父账户ID
     * @return 子账户列表
     */
    List<Account> findByParentAccountId(Long parentAccountId);

    /**
     * 查找所有激活状态的账户
     *
     * @return 激活账户列表
     */
    List<Account> findByIsActiveTrue();

    @Query("SELECT * FROM accounts WHERE account_type = :accountType AND is_active = true")
    List<Account> findActiveAccountsByType(@Param("accountType") AccountType accountType);

    @Query("SELECT * FROM accounts WHERE parent_account_id IS NULL AND is_active = true")
    List<Account> findRootAccounts();

    @Query("SELECT * FROM accounts WHERE parent_account_id = :parentId AND is_active = true")
    List<Account> findActiveSubAccounts(@Param("parentId") Long parentId);

    @Query("SELECT * FROM accounts WHERE currency_id = :currencyId AND is_active = true")
    List<Account> findAccountsByCurrency(@Param("currencyId") Long currencyId);

    @Query("SELECT * FROM accounts WHERE (account_number ILIKE :searchTerm OR account_name ILIKE :searchTerm) AND is_active = true")
    List<Account> searchActiveAccounts(@Param("searchTerm") String searchTerm);

    @Query("SELECT SUM(current_balance) FROM accounts WHERE account_type = :accountType AND is_active = true")
    java.math.BigDecimal getTotalBalanceByAccountType(@Param("accountType") AccountType accountType);

    @Query("SELECT * FROM accounts WHERE account_type IN (:types) AND is_active = true ORDER BY account_number")
    List<Account> findAccountsByTypes(@Param("types") List<AccountType> types);

    boolean existsByAccountNumber(String accountNumber);

    @Query("SELECT COUNT(*) FROM accounts WHERE parent_account_id = :parentId AND is_active = true")
    long countSubAccounts(@Param("parentId") Long parentId);
}
