package org.example.ledgerbased.repository;

import org.example.ledgerbased.model.User;
import org.example.ledgerbased.model.UserRole;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends CrudRepository<User, Long> {

    Optional<User> findByUsername(String username);

    Optional<User> findByEmail(String email);

    List<User> findByRole(UserRole role);

    List<User> findByIsActiveTrue();

    @Query("SELECT * FROM users WHERE role = :role AND is_active = true")
    List<User> findActiveUsersByRole(@Param("role") UserRole role);

    @Query("SELECT COUNT(*) FROM users WHERE role = :role AND is_active = true")
    long countActiveUsersByRole(@Param("role") UserRole role);

    @Query("SELECT * FROM users WHERE (first_name ILIKE :searchTerm OR last_name ILIKE :searchTerm OR username ILIKE :searchTerm) AND is_active = true")
    List<User> searchActiveUsers(@Param("searchTerm") String searchTerm);

    boolean existsByUsername(String username);

    boolean existsByEmail(String email);
}
