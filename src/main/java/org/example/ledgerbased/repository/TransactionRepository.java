package org.example.ledgerbased.repository;

import org.example.ledgerbased.model.Transaction;
import org.example.ledgerbased.model.TransactionStatus;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface TransactionRepository extends CrudRepository<Transaction, Long> {

    Optional<Transaction> findByTransactionNumber(String transactionNumber);

    List<Transaction> findByStatus(TransactionStatus status);

    List<Transaction> findByCreatedBy(Long createdBy);

    @Query("SELECT * FROM transactions WHERE transaction_date BETWEEN :startDate AND :endDate ORDER BY transaction_date DESC")
    List<Transaction> findByDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    @Query("SELECT * FROM transactions WHERE status = :status AND transaction_date BETWEEN :startDate AND :endDate")
    List<Transaction> findByStatusAndDateRange(@Param("status") TransactionStatus status, 
                                              @Param("startDate") LocalDate startDate, 
                                              @Param("endDate") LocalDate endDate);

    @Query("SELECT * FROM transactions WHERE created_by = :userId AND status = :status ORDER BY created_at DESC")
    List<Transaction> findByCreatedByAndStatus(@Param("userId") Long userId, @Param("status") TransactionStatus status);

    @Query("SELECT * FROM transactions WHERE total_amount BETWEEN :minAmount AND :maxAmount ORDER BY total_amount DESC")
    List<Transaction> findByAmountRange(@Param("minAmount") BigDecimal minAmount, @Param("maxAmount") BigDecimal maxAmount);

    @Query("SELECT COUNT(*) FROM transactions WHERE status = :status")
    long countByStatus(@Param("status") TransactionStatus status);

    @Query("SELECT SUM(total_amount) FROM transactions WHERE status = :status AND transaction_date BETWEEN :startDate AND :endDate")
    BigDecimal sumAmountByStatusAndDateRange(@Param("status") TransactionStatus status, 
                                           @Param("startDate") LocalDate startDate, 
                                           @Param("endDate") LocalDate endDate);

    @Query("SELECT * FROM transactions WHERE (description ILIKE :searchTerm OR reference_number ILIKE :searchTerm OR transaction_number ILIKE :searchTerm) ORDER BY created_at DESC")
    List<Transaction> searchTransactions(@Param("searchTerm") String searchTerm);

    @Query("SELECT * FROM transactions WHERE status = 'PENDING' AND created_at < :cutoffTime")
    List<Transaction> findPendingTransactionsOlderThan(@Param("cutoffTime") java.time.LocalDateTime cutoffTime);

    boolean existsByTransactionNumber(String transactionNumber);

    @Query("SELECT * FROM transactions ORDER BY created_at DESC LIMIT :limit")
    List<Transaction> findRecentTransactions(@Param("limit") int limit);
}
