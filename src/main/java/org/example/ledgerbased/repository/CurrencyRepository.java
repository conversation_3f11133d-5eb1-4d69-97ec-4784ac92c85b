package org.example.ledgerbased.repository;

import org.example.ledgerbased.model.Currency;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CurrencyRepository extends CrudRepository<Currency, Long> {

    Optional<Currency> findByCode(String code);

    List<Currency> findByIsActiveTrue();

    @Query("SELECT * FROM currencies WHERE code IN (:codes) AND is_active = true")
    List<Currency> findActiveCurrenciesByCodes(@Param("codes") List<String> codes);

    boolean existsByCode(String code);

    @Query("SELECT * FROM currencies WHERE name ILIKE :searchTerm OR code ILIKE :searchTerm")
    List<Currency> searchCurrencies(@Param("searchTerm") String searchTerm);
}
