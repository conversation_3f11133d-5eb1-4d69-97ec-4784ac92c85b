package org.example.ledgerbased.repository;

import org.example.ledgerbased.model.EntryType;
import org.example.ledgerbased.model.TransactionEntry;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface TransactionEntryRepository extends CrudRepository<TransactionEntry, Long> {

    List<TransactionEntry> findByTransactionId(Long transactionId);

    List<TransactionEntry> findByAccountId(Long accountId);

    List<TransactionEntry> findByAccountIdAndEntryType(Long accountId, EntryType entryType);

    @Query("SELECT te.* FROM transaction_entries te " +
           "JOIN transactions t ON te.transaction_id = t.id " +
           "WHERE te.account_id = :accountId AND t.transaction_date BETWEEN :startDate AND :endDate " +
           "ORDER BY t.transaction_date, te.created_at")
    List<TransactionEntry> findByAccountIdAndDateRange(@Param("accountId") Long accountId,
                                                       @Param("startDate") LocalDate startDate,
                                                       @Param("endDate") LocalDate endDate);

    @Query("SELECT SUM(te.amount) FROM transaction_entries te " +
           "JOIN transactions t ON te.transaction_id = t.id " +
           "WHERE te.account_id = :accountId AND te.entry_type = :entryType AND t.status = 'POSTED'")
    BigDecimal sumAmountByAccountAndEntryType(@Param("accountId") Long accountId, @Param("entryType") EntryType entryType);

    @Query("SELECT te.* FROM transaction_entries te " +
           "JOIN transactions t ON te.transaction_id = t.id " +
           "WHERE te.account_id = :accountId AND t.status = 'POSTED' " +
           "ORDER BY t.transaction_date DESC, te.created_at DESC")
    List<TransactionEntry> findPostedEntriesByAccount(@Param("accountId") Long accountId);

    @Query("SELECT te.* FROM transaction_entries te " +
           "JOIN transactions t ON te.transaction_id = t.id " +
           "WHERE te.account_id IN (:accountIds) AND t.transaction_date BETWEEN :startDate AND :endDate " +
           "ORDER BY t.transaction_date, te.created_at")
    List<TransactionEntry> findByAccountIdsAndDateRange(@Param("accountIds") List<Long> accountIds,
                                                        @Param("startDate") LocalDate startDate,
                                                        @Param("endDate") LocalDate endDate);

    @Query("SELECT COUNT(*) FROM transaction_entries WHERE transaction_id = :transactionId")
    long countByTransactionId(@Param("transactionId") Long transactionId);

    @Query("SELECT SUM(amount) FROM transaction_entries WHERE transaction_id = :transactionId AND entry_type = :entryType")
    BigDecimal sumAmountByTransactionAndEntryType(@Param("transactionId") Long transactionId, @Param("entryType") EntryType entryType);

    @Query("DELETE FROM transaction_entries WHERE transaction_id = :transactionId")
    void deleteByTransactionId(@Param("transactionId") Long transactionId);
}
