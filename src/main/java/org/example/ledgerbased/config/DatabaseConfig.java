package org.example.ledgerbased.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jdbc.repository.config.EnableJdbcRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EnableJdbcRepositories(basePackages = "org.example.ledgerbased.repository")
@EnableTransactionManagement
public class DatabaseConfig {
    
    // Spring Data JDBC configuration is handled by auto-configuration
    // This class serves as a central place for any custom database configurations
}
