package org.example.ledgerbased.exception;

public class LedgerException extends RuntimeException {
    
    private final String errorCode;
    
    public LedgerException(String message) {
        super(message);
        this.errorCode = "LEDGER_ERROR";
    }
    
    public LedgerException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public LedgerException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "LEDGER_ERROR";
    }
    
    public LedgerException(String message, String errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
}
