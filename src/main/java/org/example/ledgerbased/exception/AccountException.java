package org.example.ledgerbased.exception;

public class AccountException extends LedgerException {
    
    public AccountException(String message) {
        super(message, "ACCOUNT_ERROR");
    }
    
    public AccountException(String message, Throwable cause) {
        super(message, "ACCOUNT_ERROR", cause);
    }
    
    public static class AccountNotFoundException extends AccountException {
        public AccountNotFoundException(String accountNumber) {
            super("Account not found: " + accountNumber);
        }
        
        public AccountNotFoundException(Long accountId) {
            super("Account not found with ID: " + accountId);
        }
    }
    
    public static class DuplicateAccountNumberException extends AccountException {
        public DuplicateAccountNumberException(String accountNumber) {
            super("Account number already exists: " + accountNumber);
        }
    }
    
    public static class InactiveAccountException extends AccountException {
        public InactiveAccountException(String accountNumber) {
            super("Account is inactive: " + accountNumber);
        }
    }
    
    public static class InsufficientBalanceException extends AccountException {
        public InsufficientBalanceException(String accountNumber, java.math.BigDecimal currentBalance, java.math.BigDecimal requiredAmount) {
            super(String.format("Insufficient balance in account %s. Current: %s, Required: %s", 
                  accountNumber, currentBalance, requiredAmount));
        }
    }
    
    public static class InvalidAccountHierarchyException extends AccountException {
        public InvalidAccountHierarchyException(String message) {
            super("Invalid account hierarchy: " + message);
        }
    }
}
