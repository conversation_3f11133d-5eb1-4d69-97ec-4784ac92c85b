package org.example.ledgerbased.exception;

public class TransactionException extends LedgerException {
    
    public TransactionException(String message) {
        super(message, "TRANSACTION_ERROR");
    }
    
    public TransactionException(String message, Throwable cause) {
        super(message, "TRANSACTION_ERROR", cause);
    }
    
    public static class UnbalancedTransactionException extends TransactionException {
        public UnbalancedTransactionException(String message) {
            super("Transaction is not balanced: " + message);
        }
    }
    
    public static class InvalidTransactionStatusException extends TransactionException {
        public InvalidTransactionStatusException(String message) {
            super("Invalid transaction status: " + message);
        }
    }
    
    public static class TransactionNotFoundException extends TransactionException {
        public TransactionNotFoundException(String transactionNumber) {
            super("Transaction not found: " + transactionNumber);
        }
        
        public TransactionNotFoundException(Long transactionId) {
            super("Transaction not found with ID: " + transactionId);
        }
    }
    
    public static class DuplicateTransactionNumberException extends TransactionException {
        public DuplicateTransactionNumberException(String transactionNumber) {
            super("Transaction number already exists: " + transactionNumber);
        }
    }
}
