package org.example.ledgerbased.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Table("transactions")
public class Transaction {
    
    @Id
    private Long id;
    private String transactionNumber;
    private String description;
    private String referenceNumber;
    private LocalDate transactionDate;
    private TransactionStatus status;
    private BigDecimal totalAmount;
    private Long currencyId;
    private Long createdBy;
    private Long approvedBy;
    private LocalDateTime approvedAt;
    private LocalDateTime postedAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Transient field for entries (not persisted directly)
    private List<TransactionEntry> entries;

    public Transaction() {}

    public Transaction(String transactionNumber, String description, LocalDate transactionDate,
                      BigDecimal totalAmount, Long currencyId, Long createdBy) {
        this.transactionNumber = transactionNumber;
        this.description = description;
        this.transactionDate = transactionDate;
        this.status = TransactionStatus.PENDING;
        this.totalAmount = totalAmount;
        this.currencyId = currencyId;
        this.createdBy = createdBy;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getTransactionNumber() { return transactionNumber; }
    public void setTransactionNumber(String transactionNumber) { this.transactionNumber = transactionNumber; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getReferenceNumber() { return referenceNumber; }
    public void setReferenceNumber(String referenceNumber) { this.referenceNumber = referenceNumber; }

    public LocalDate getTransactionDate() { return transactionDate; }
    public void setTransactionDate(LocalDate transactionDate) { this.transactionDate = transactionDate; }

    public TransactionStatus getStatus() { return status; }
    public void setStatus(TransactionStatus status) { this.status = status; }

    public BigDecimal getTotalAmount() { return totalAmount; }
    public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }

    public Long getCurrencyId() { return currencyId; }
    public void setCurrencyId(Long currencyId) { this.currencyId = currencyId; }

    public Long getCreatedBy() { return createdBy; }
    public void setCreatedBy(Long createdBy) { this.createdBy = createdBy; }

    public Long getApprovedBy() { return approvedBy; }
    public void setApprovedBy(Long approvedBy) { this.approvedBy = approvedBy; }

    public LocalDateTime getApprovedAt() { return approvedAt; }
    public void setApprovedAt(LocalDateTime approvedAt) { this.approvedAt = approvedAt; }

    public LocalDateTime getPostedAt() { return postedAt; }
    public void setPostedAt(LocalDateTime postedAt) { this.postedAt = postedAt; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public List<TransactionEntry> getEntries() { return entries; }
    public void setEntries(List<TransactionEntry> entries) { this.entries = entries; }

    // Business methods
    public void approve(Long approvedBy) {
        if (!status.canBeApproved()) {
            throw new IllegalStateException("Transaction cannot be approved in current status: " + status);
        }
        this.status = TransactionStatus.APPROVED;
        this.approvedBy = approvedBy;
        this.approvedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public void post() {
        if (!status.canBePosted()) {
            throw new IllegalStateException("Transaction cannot be posted in current status: " + status);
        }
        this.status = TransactionStatus.POSTED;
        this.postedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public void reverse() {
        if (!status.canBeReversed()) {
            throw new IllegalStateException("Transaction cannot be reversed in current status: " + status);
        }
        this.status = TransactionStatus.REVERSED;
        this.updatedAt = LocalDateTime.now();
    }

    public boolean isBalanced() {
        if (entries == null || entries.isEmpty()) {
            return false;
        }

        BigDecimal totalDebits = BigDecimal.ZERO;
        BigDecimal totalCredits = BigDecimal.ZERO;

        for (TransactionEntry entry : entries) {
            if (entry.getEntryType() == EntryType.DEBIT) {
                totalDebits = totalDebits.add(entry.getAmount());
            } else {
                totalCredits = totalCredits.add(entry.getAmount());
            }
        }

        return totalDebits.compareTo(totalCredits) == 0;
    }

    @Override
    public String toString() {
        return "Transaction{" +
                "id=" + id +
                ", transactionNumber='" + transactionNumber + '\'' +
                ", description='" + description + '\'' +
                ", transactionDate=" + transactionDate +
                ", status=" + status +
                ", totalAmount=" + totalAmount +
                '}';
    }
}
