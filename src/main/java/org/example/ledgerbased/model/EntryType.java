package org.example.ledgerbased.model;

public enum EntryType {
    DEBIT("Debit entry - increases assets and expenses, decreases liabilities, equity, and revenue"),
    CREDIT("Credit entry - increases liabilities, equity, and revenue, decreases assets and expenses");

    private final String description;

    EntryType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public EntryType opposite() {
        return this == DEBIT ? CREDIT : DEBIT;
    }

    public String getSymbol() {
        return this == DEBIT ? "Dr" : "Cr";
    }
}
