package org.example.ledgerbased.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Table("transaction_entries")
public class TransactionEntry {
    
    @Id
    private Long id;
    private Long transactionId;
    private Long accountId;
    private EntryType entryType;
    private BigDecimal amount;
    private String description;
    private LocalDateTime createdAt;

    public TransactionEntry() {}

    public TransactionEntry(Long transactionId, Long accountId, EntryType entryType,
                           BigDecimal amount, String description) {
        this.transactionId = transactionId;
        this.accountId = accountId;
        this.entryType = entryType;
        this.amount = amount;
        this.description = description;
        this.createdAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getTransactionId() { return transactionId; }
    public void setTransactionId(Long transactionId) { this.transactionId = transactionId; }

    public Long getAccountId() { return accountId; }
    public void setAccountId(Long accountId) { this.accountId = accountId; }

    public EntryType getEntryType() { return entryType; }
    public void setEntryType(EntryType entryType) { this.entryType = entryType; }

    public BigDecimal getAmount() { return amount; }
    public void setAmount(BigDecimal amount) { this.amount = amount; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    // Business methods
    public boolean isDebit() {
        return entryType == EntryType.DEBIT;
    }

    public boolean isCredit() {
        return entryType == EntryType.CREDIT;
    }

    public String getFormattedAmount() {
        return entryType.getSymbol() + " " + amount.toString();
    }

    @Override
    public String toString() {
        return "TransactionEntry{" +
                "id=" + id +
                ", transactionId=" + transactionId +
                ", accountId=" + accountId +
                ", entryType=" + entryType +
                ", amount=" + amount +
                ", description='" + description + '\'' +
                '}';
    }
}
