package org.example.ledgerbased.model;

public enum TransactionStatus {
    PENDING("Transaction created but not yet approved"),
    APPROVED("Transaction approved but not yet posted to ledger"),
    POSTED("Transaction posted to ledger and affecting account balances"),
    REVERSED("Transaction has been reversed");

    private final String description;

    TransactionStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public boolean canBeModified() {
        return this == PENDING;
    }

    public boolean canBeApproved() {
        return this == PENDING;
    }

    public boolean canBePosted() {
        return this == APPROVED;
    }

    public boolean canBeReversed() {
        return this == POSTED;
    }

    public boolean affectsBalance() {
        return this == POSTED;
    }
}
