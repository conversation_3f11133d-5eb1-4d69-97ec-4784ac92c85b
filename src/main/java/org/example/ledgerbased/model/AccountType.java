package org.example.ledgerbased.model;

public enum AccountType {
    ASSET("Assets - Resources owned by the company"),
    LIABILITY("Liabilities - Debts owed by the company"),
    EQUITY("Equity - Owner's interest in the company"),
    REVENUE("Revenue - Income earned by the company"),
    EXPENSE("Expenses - Costs incurred by the company");

    private final String description;

    AccountType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Determines the normal balance side for this account type
     * @return true if normal balance is debit, false if credit
     */
    public boolean isNormalBalanceDebit() {
        return this == ASSET || this == EXPENSE;
    }

    /**
     * Determines if this account type increases with debits
     * @return true if increases with debits, false if increases with credits
     */
    public boolean increasesWithDebit() {
        return isNormalBalanceDebit();
    }

    /**
     * Determines if this account type can have a negative balance
     * @return true if negative balance is allowed
     */
    public boolean allowsNegativeBalance() {
        // Generally, only liability and equity accounts can have negative balances
        // in certain circumstances, but for strict accounting, we'll be restrictive
        return false;
    }

    /**
     * Gets the financial statement category
     * @return the statement where this account type appears
     */
    public String getFinancialStatement() {
        switch (this) {
            case ASSET:
            case LIABILITY:
            case EQUITY:
                return "Balance Sheet";
            case REVENUE:
            case EXPENSE:
                return "Income Statement";
            default:
                return "Unknown";
        }
    }
}
