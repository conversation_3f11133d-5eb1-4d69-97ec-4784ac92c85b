package org.example.ledgerbased.controller;

import org.example.ledgerbased.dto.CreateAccountRequest;
import org.example.ledgerbased.model.Account;
import org.example.ledgerbased.model.AccountType;
import org.example.ledgerbased.service.AccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping("/api/accounts")
public class AccountController {

    private final AccountService accountService;

    @Autowired
    public AccountController(AccountService accountService) {
        this.accountService = accountService;
    }

    @PostMapping
    public ResponseEntity<Account> createAccount(@Valid @RequestBody CreateAccountRequest request) {
        Account account;
        
        if (request.getParentAccountId() != null) {
            account = accountService.createSubAccount(
                request.getAccountNumber(),
                request.getAccountName(),
                request.getParentAccountId(),
                request.getCurrencyId(),
                request.getOpeningBalance(),
                1L // TODO: Get from security context
            );
        } else {
            account = accountService.createAccount(
                request.getAccountNumber(),
                request.getAccountName(),
                request.getAccountType(),
                request.getCurrencyId(),
                request.getOpeningBalance(),
                1L // TODO: Get from security context
            );
        }
        
        return new ResponseEntity<>(account, HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Account> getAccount(@PathVariable Long id) {
        Account account = accountService.getAccountById(id);
        return ResponseEntity.ok(account);
    }

    @GetMapping("/number/{accountNumber}")
    public ResponseEntity<Account> getAccountByNumber(@PathVariable String accountNumber) {
        Account account = accountService.getAccountByNumber(accountNumber);
        return ResponseEntity.ok(account);
    }

    @GetMapping
    public ResponseEntity<List<Account>> getAllAccounts(
            @RequestParam(required = false) AccountType type,
            @RequestParam(required = false) String search) {
        
        List<Account> accounts;
        
        if (search != null && !search.trim().isEmpty()) {
            accounts = accountService.searchAccounts(search.trim());
        } else if (type != null) {
            accounts = accountService.getAccountsByType(type);
        } else {
            accounts = accountService.getAllActiveAccounts();
        }
        
        return ResponseEntity.ok(accounts);
    }

    @GetMapping("/root")
    public ResponseEntity<List<Account>> getRootAccounts() {
        List<Account> accounts = accountService.getRootAccounts();
        return ResponseEntity.ok(accounts);
    }

    @GetMapping("/{parentId}/sub-accounts")
    public ResponseEntity<List<Account>> getSubAccounts(@PathVariable Long parentId) {
        List<Account> accounts = accountService.getSubAccounts(parentId);
        return ResponseEntity.ok(accounts);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Account> updateAccount(@PathVariable Long id, 
                                               @RequestParam String accountName) {
        Account account = accountService.updateAccount(id, accountName);
        return ResponseEntity.ok(account);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deactivateAccount(@PathVariable Long id) {
        accountService.deactivateAccount(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/balance-sheet")
    public ResponseEntity<List<Account>> getBalanceSheetAccounts() {
        List<Account> accounts = accountService.getAccountsForBalanceSheet();
        return ResponseEntity.ok(accounts);
    }

    @GetMapping("/income-statement")
    public ResponseEntity<List<Account>> getIncomeStatementAccounts() {
        List<Account> accounts = accountService.getAccountsForIncomeStatement();
        return ResponseEntity.ok(accounts);
    }

    @GetMapping("/totals/{accountType}")
    public ResponseEntity<BigDecimal> getTotalByAccountType(@PathVariable AccountType accountType) {
        BigDecimal total = accountService.getTotalBalanceByAccountType(accountType);
        return ResponseEntity.ok(total);
    }

    @GetMapping("/validate-equation")
    public ResponseEntity<Boolean> validateAccountingEquation() {
        boolean isValid = accountService.validateAccountingEquation();
        return ResponseEntity.ok(isValid);
    }
}
