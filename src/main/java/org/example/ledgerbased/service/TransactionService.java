package org.example.ledgerbased.service;

import org.example.ledgerbased.exception.TransactionException;
import org.example.ledgerbased.model.*;
import org.example.ledgerbased.repository.TransactionRepository;
import org.example.ledgerbased.repository.TransactionEntryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 交易服务类，提供财务交易的核心业务逻辑
 * 实现复式记账原理，确保每个交易的借贷平衡
 * 支持交易的完整生命周期管理：创建、审批、过账、冲销
 */
@Service
@Transactional
public class TransactionService {

    /** 交易数据访问对象 */
    private final TransactionRepository transactionRepository;

    /** 交易分录数据访问对象 */
    private final TransactionEntryRepository transactionEntryRepository;

    /** 账户服务，用于验证账户和更新余额 */
    private final AccountService accountService;

    /**
     * 构造函数，注入所需的依赖服务
     *
     * @param transactionRepository 交易数据访问对象
     * @param transactionEntryRepository 交易分录数据访问对象
     * @param accountService 账户服务
     */
    @Autowired
    public TransactionService(TransactionRepository transactionRepository,
                            TransactionEntryRepository transactionEntryRepository,
                            AccountService accountService) {
        this.transactionRepository = transactionRepository;
        this.transactionEntryRepository = transactionEntryRepository;
        this.accountService = accountService;
    }

    /**
     * 创建新的财务交易
     * 实现复式记账原理，验证借贷平衡，生成唯一交易编号
     * 交易创建后状态为PENDING，需要审批后才能过账
     *
     * @param description 交易描述
     * @param transactionDate 交易日期
     * @param entries 交易分录列表，必须至少包含2个分录
     * @param currencyId 货币ID
     * @param createdBy 创建者用户ID
     * @return 创建的交易对象
     * @throws TransactionException.UnbalancedTransactionException 如果借贷不平衡
     * @throws TransactionException 如果分录验证失败
     */
    public Transaction createTransaction(String description, LocalDate transactionDate,
                                       List<TransactionEntry> entries, Long currencyId, Long createdBy) {

        // 生成唯一交易编号
        String transactionNumber = generateTransactionNumber();

        // 验证分录
        validateTransactionEntries(entries);

        // 计算交易总金额
        BigDecimal totalAmount = calculateTotalAmount(entries);

        // 创建交易对象
        Transaction transaction = new Transaction(transactionNumber, description, transactionDate,
                                                totalAmount, currencyId, createdBy);
        transaction.setEntries(entries);

        // 验证交易借贷平衡
        if (!transaction.isBalanced()) {
            throw new TransactionException.UnbalancedTransactionException(
                "Debits and credits must be equal");
        }

        // 保存交易
        Transaction savedTransaction = transactionRepository.save(transaction);

        // 保存分录
        for (TransactionEntry entry : entries) {
            entry.setTransactionId(savedTransaction.getId());
            transactionEntryRepository.save(entry);
        }

        return savedTransaction;
    }

    @Transactional(readOnly = true)
    public Transaction getTransactionById(Long transactionId) {
        Transaction transaction = transactionRepository.findById(transactionId)
                .orElseThrow(() -> new TransactionException.TransactionNotFoundException(transactionId));
        
        // Load entries
        List<TransactionEntry> entries = transactionEntryRepository.findByTransactionId(transactionId);
        transaction.setEntries(entries);
        
        return transaction;
    }

    @Transactional(readOnly = true)
    public Transaction getTransactionByNumber(String transactionNumber) {
        Transaction transaction = transactionRepository.findByTransactionNumber(transactionNumber)
                .orElseThrow(() -> new TransactionException.TransactionNotFoundException(transactionNumber));
        
        // Load entries
        List<TransactionEntry> entries = transactionEntryRepository.findByTransactionId(transaction.getId());
        transaction.setEntries(entries);
        
        return transaction;
    }

    public Transaction approveTransaction(Long transactionId, Long approvedBy) {
        Transaction transaction = getTransactionById(transactionId);
        
        if (!transaction.getStatus().canBeApproved()) {
            throw new TransactionException.InvalidTransactionStatusException(
                "Transaction cannot be approved in status: " + transaction.getStatus());
        }
        
        transaction.approve(approvedBy);
        return transactionRepository.save(transaction);
    }

    public Transaction postTransaction(Long transactionId) {
        Transaction transaction = getTransactionById(transactionId);
        
        if (!transaction.getStatus().canBePosted()) {
            throw new TransactionException.InvalidTransactionStatusException(
                "Transaction cannot be posted in status: " + transaction.getStatus());
        }
        
        // Update account balances
        for (TransactionEntry entry : transaction.getEntries()) {
            accountService.updateAccountBalance(entry.getAccountId(), entry.getAmount(), entry.getEntryType());
        }
        
        transaction.post();
        return transactionRepository.save(transaction);
    }

    public Transaction reverseTransaction(Long transactionId, String reversalReason, Long reversedBy) {
        Transaction originalTransaction = getTransactionById(transactionId);
        
        if (!originalTransaction.getStatus().canBeReversed()) {
            throw new TransactionException.InvalidTransactionStatusException(
                "Transaction cannot be reversed in status: " + originalTransaction.getStatus());
        }
        
        // Create reversal entries (opposite of original)
        List<TransactionEntry> reversalEntries = originalTransaction.getEntries().stream()
                .map(entry -> new TransactionEntry(
                    null, // Will be set when transaction is saved
                    entry.getAccountId(),
                    entry.getEntryType().opposite(),
                    entry.getAmount(),
                    "Reversal of: " + entry.getDescription()
                ))
                .toList();
        
        // Create reversal transaction
        Transaction reversalTransaction = createTransaction(
            "Reversal of " + originalTransaction.getTransactionNumber() + ": " + reversalReason,
            LocalDate.now(),
            reversalEntries,
            originalTransaction.getCurrencyId(),
            reversedBy
        );
        
        // Auto-approve and post reversal
        approveTransaction(reversalTransaction.getId(), reversedBy);
        postTransaction(reversalTransaction.getId());
        
        // Mark original as reversed
        originalTransaction.reverse();
        transactionRepository.save(originalTransaction);
        
        return reversalTransaction;
    }

    @Transactional(readOnly = true)
    public List<Transaction> getTransactionsByStatus(TransactionStatus status) {
        return transactionRepository.findByStatus(status);
    }

    @Transactional(readOnly = true)
    public List<Transaction> getTransactionsByDateRange(LocalDate startDate, LocalDate endDate) {
        return transactionRepository.findByDateRange(startDate, endDate);
    }

    @Transactional(readOnly = true)
    public List<Transaction> getTransactionsByUser(Long userId) {
        return transactionRepository.findByCreatedBy(userId);
    }

    @Transactional(readOnly = true)
    public List<Transaction> searchTransactions(String searchTerm) {
        return transactionRepository.searchTransactions("%" + searchTerm + "%");
    }

    private void validateTransactionEntries(List<TransactionEntry> entries) {
        if (entries == null || entries.size() < 2) {
            throw new TransactionException("Transaction must have at least 2 entries");
        }
        
        for (TransactionEntry entry : entries) {
            if (entry.getAmount() == null || entry.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                throw new TransactionException("Entry amount must be positive");
            }
            
            if (entry.getAccountId() == null) {
                throw new TransactionException("Entry must have an account");
            }
            
            // Validate account exists and is active
            Account account = accountService.getAccountById(entry.getAccountId());
            if (!account.isActive()) {
                throw new TransactionException("Cannot use inactive account: " + account.getAccountNumber());
            }
        }
    }

    private BigDecimal calculateTotalAmount(List<TransactionEntry> entries) {
        return entries.stream()
                .filter(entry -> entry.getEntryType() == EntryType.DEBIT)
                .map(TransactionEntry::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private String generateTransactionNumber() {
        String prefix = "TXN";
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        return prefix + "-" + timestamp + "-" + uuid;
    }
}
