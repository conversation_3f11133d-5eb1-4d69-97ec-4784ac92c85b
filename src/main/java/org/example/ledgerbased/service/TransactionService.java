package org.example.ledgerbased.service;

import org.example.ledgerbased.exception.TransactionException;
import org.example.ledgerbased.model.*;
import org.example.ledgerbased.repository.TransactionRepository;
import org.example.ledgerbased.repository.TransactionEntryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@Transactional
public class TransactionService {

    private final TransactionRepository transactionRepository;
    private final TransactionEntryRepository transactionEntryRepository;
    private final AccountService accountService;

    @Autowired
    public TransactionService(TransactionRepository transactionRepository,
                            TransactionEntryRepository transactionEntryRepository,
                            AccountService accountService) {
        this.transactionRepository = transactionRepository;
        this.transactionEntryRepository = transactionEntryRepository;
        this.accountService = accountService;
    }

    public Transaction createTransaction(String description, LocalDate transactionDate,
                                       List<TransactionEntry> entries, Long currencyId, Long createdBy) {
        
        // Generate unique transaction number
        String transactionNumber = generateTransactionNumber();
        
        // Validate entries
        validateTransactionEntries(entries);
        
        // Calculate total amount
        BigDecimal totalAmount = calculateTotalAmount(entries);
        
        // Create transaction
        Transaction transaction = new Transaction(transactionNumber, description, transactionDate,
                                                totalAmount, currencyId, createdBy);
        transaction.setEntries(entries);
        
        // Validate transaction is balanced
        if (!transaction.isBalanced()) {
            throw new TransactionException.UnbalancedTransactionException(
                "Debits and credits must be equal");
        }
        
        // Save transaction
        Transaction savedTransaction = transactionRepository.save(transaction);
        
        // Save entries
        for (TransactionEntry entry : entries) {
            entry.setTransactionId(savedTransaction.getId());
            transactionEntryRepository.save(entry);
        }
        
        return savedTransaction;
    }

    @Transactional(readOnly = true)
    public Transaction getTransactionById(Long transactionId) {
        Transaction transaction = transactionRepository.findById(transactionId)
                .orElseThrow(() -> new TransactionException.TransactionNotFoundException(transactionId));
        
        // Load entries
        List<TransactionEntry> entries = transactionEntryRepository.findByTransactionId(transactionId);
        transaction.setEntries(entries);
        
        return transaction;
    }

    @Transactional(readOnly = true)
    public Transaction getTransactionByNumber(String transactionNumber) {
        Transaction transaction = transactionRepository.findByTransactionNumber(transactionNumber)
                .orElseThrow(() -> new TransactionException.TransactionNotFoundException(transactionNumber));
        
        // Load entries
        List<TransactionEntry> entries = transactionEntryRepository.findByTransactionId(transaction.getId());
        transaction.setEntries(entries);
        
        return transaction;
    }

    public Transaction approveTransaction(Long transactionId, Long approvedBy) {
        Transaction transaction = getTransactionById(transactionId);
        
        if (!transaction.getStatus().canBeApproved()) {
            throw new TransactionException.InvalidTransactionStatusException(
                "Transaction cannot be approved in status: " + transaction.getStatus());
        }
        
        transaction.approve(approvedBy);
        return transactionRepository.save(transaction);
    }

    public Transaction postTransaction(Long transactionId) {
        Transaction transaction = getTransactionById(transactionId);
        
        if (!transaction.getStatus().canBePosted()) {
            throw new TransactionException.InvalidTransactionStatusException(
                "Transaction cannot be posted in status: " + transaction.getStatus());
        }
        
        // Update account balances
        for (TransactionEntry entry : transaction.getEntries()) {
            accountService.updateAccountBalance(entry.getAccountId(), entry.getAmount(), entry.getEntryType());
        }
        
        transaction.post();
        return transactionRepository.save(transaction);
    }

    public Transaction reverseTransaction(Long transactionId, String reversalReason, Long reversedBy) {
        Transaction originalTransaction = getTransactionById(transactionId);
        
        if (!originalTransaction.getStatus().canBeReversed()) {
            throw new TransactionException.InvalidTransactionStatusException(
                "Transaction cannot be reversed in status: " + originalTransaction.getStatus());
        }
        
        // Create reversal entries (opposite of original)
        List<TransactionEntry> reversalEntries = originalTransaction.getEntries().stream()
                .map(entry -> new TransactionEntry(
                    null, // Will be set when transaction is saved
                    entry.getAccountId(),
                    entry.getEntryType().opposite(),
                    entry.getAmount(),
                    "Reversal of: " + entry.getDescription()
                ))
                .toList();
        
        // Create reversal transaction
        Transaction reversalTransaction = createTransaction(
            "Reversal of " + originalTransaction.getTransactionNumber() + ": " + reversalReason,
            LocalDate.now(),
            reversalEntries,
            originalTransaction.getCurrencyId(),
            reversedBy
        );
        
        // Auto-approve and post reversal
        approveTransaction(reversalTransaction.getId(), reversedBy);
        postTransaction(reversalTransaction.getId());
        
        // Mark original as reversed
        originalTransaction.reverse();
        transactionRepository.save(originalTransaction);
        
        return reversalTransaction;
    }

    @Transactional(readOnly = true)
    public List<Transaction> getTransactionsByStatus(TransactionStatus status) {
        return transactionRepository.findByStatus(status);
    }

    @Transactional(readOnly = true)
    public List<Transaction> getTransactionsByDateRange(LocalDate startDate, LocalDate endDate) {
        return transactionRepository.findByDateRange(startDate, endDate);
    }

    @Transactional(readOnly = true)
    public List<Transaction> getTransactionsByUser(Long userId) {
        return transactionRepository.findByCreatedBy(userId);
    }

    @Transactional(readOnly = true)
    public List<Transaction> searchTransactions(String searchTerm) {
        return transactionRepository.searchTransactions("%" + searchTerm + "%");
    }

    private void validateTransactionEntries(List<TransactionEntry> entries) {
        if (entries == null || entries.size() < 2) {
            throw new TransactionException("Transaction must have at least 2 entries");
        }
        
        for (TransactionEntry entry : entries) {
            if (entry.getAmount() == null || entry.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                throw new TransactionException("Entry amount must be positive");
            }
            
            if (entry.getAccountId() == null) {
                throw new TransactionException("Entry must have an account");
            }
            
            // Validate account exists and is active
            Account account = accountService.getAccountById(entry.getAccountId());
            if (!account.isActive()) {
                throw new TransactionException("Cannot use inactive account: " + account.getAccountNumber());
            }
        }
    }

    private BigDecimal calculateTotalAmount(List<TransactionEntry> entries) {
        return entries.stream()
                .filter(entry -> entry.getEntryType() == EntryType.DEBIT)
                .map(TransactionEntry::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private String generateTransactionNumber() {
        String prefix = "TXN";
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        return prefix + "-" + timestamp + "-" + uuid;
    }
}
