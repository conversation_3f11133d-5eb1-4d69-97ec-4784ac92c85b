package org.example.ledgerbased.service;

import org.example.ledgerbased.exception.AccountException;
import org.example.ledgerbased.model.Account;
import org.example.ledgerbased.model.AccountType;
import org.example.ledgerbased.model.EntryType;
import org.example.ledgerbased.repository.AccountRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class AccountService {

    private final AccountRepository accountRepository;

    @Autowired
    public AccountService(AccountRepository accountRepository) {
        this.accountRepository = accountRepository;
    }

    public Account createAccount(String accountNumber, String accountName, AccountType accountType,
                               Long currencyId, BigDecimal openingBalance, Long createdBy) {
        
        // Validate account number uniqueness
        if (accountRepository.existsByAccountNumber(accountNumber)) {
            throw new AccountException.DuplicateAccountNumberException(accountNumber);
        }

        Account account = new Account(accountNumber, accountName, accountType, currencyId, openingBalance, createdBy);
        return accountRepository.save(account);
    }

    public Account createSubAccount(String accountNumber, String accountName, Long parentAccountId,
                                  Long currencyId, BigDecimal openingBalance, Long createdBy) {
        
        // Validate parent account exists
        Account parentAccount = getAccountById(parentAccountId);
        
        // Validate account number uniqueness
        if (accountRepository.existsByAccountNumber(accountNumber)) {
            throw new AccountException.DuplicateAccountNumberException(accountNumber);
        }

        Account account = new Account(accountNumber, accountName, parentAccount.getAccountType(), 
                                    currencyId, openingBalance, createdBy);
        account.setParentAccountId(parentAccountId);
        return accountRepository.save(account);
    }

    @Transactional(readOnly = true)
    public Account getAccountById(Long accountId) {
        return accountRepository.findById(accountId)
                .orElseThrow(() -> new AccountException.AccountNotFoundException(accountId));
    }

    @Transactional(readOnly = true)
    public Account getAccountByNumber(String accountNumber) {
        return accountRepository.findByAccountNumber(accountNumber)
                .orElseThrow(() -> new AccountException.AccountNotFoundException(accountNumber));
    }

    @Transactional(readOnly = true)
    public List<Account> getAccountsByType(AccountType accountType) {
        return accountRepository.findActiveAccountsByType(accountType);
    }

    @Transactional(readOnly = true)
    public List<Account> getRootAccounts() {
        return accountRepository.findRootAccounts();
    }

    @Transactional(readOnly = true)
    public List<Account> getSubAccounts(Long parentAccountId) {
        return accountRepository.findActiveSubAccounts(parentAccountId);
    }

    @Transactional(readOnly = true)
    public List<Account> getAllActiveAccounts() {
        return accountRepository.findByIsActiveTrue();
    }

    @Transactional(readOnly = true)
    public List<Account> searchAccounts(String searchTerm) {
        return accountRepository.searchActiveAccounts("%" + searchTerm + "%");
    }

    public Account updateAccount(Long accountId, String accountName) {
        Account account = getAccountById(accountId);
        account.setAccountName(accountName);
        account.setUpdatedAt(LocalDateTime.now());
        return accountRepository.save(account);
    }

    public void deactivateAccount(Long accountId) {
        Account account = getAccountById(accountId);
        
        // Check if account has sub-accounts
        long subAccountCount = accountRepository.countSubAccounts(accountId);
        if (subAccountCount > 0) {
            throw new AccountException("Cannot deactivate account with active sub-accounts");
        }

        account.setActive(false);
        account.setUpdatedAt(LocalDateTime.now());
        accountRepository.save(account);
    }

    public void updateAccountBalance(Long accountId, BigDecimal amount, EntryType entryType) {
        Account account = getAccountById(accountId);
        
        if (!account.isActive()) {
            throw new AccountException.InactiveAccountException(account.getAccountNumber());
        }

        account.updateBalance(amount, entryType);
        accountRepository.save(account);
    }

    @Transactional(readOnly = true)
    public BigDecimal getTotalBalanceByAccountType(AccountType accountType) {
        BigDecimal total = accountRepository.getTotalBalanceByAccountType(accountType);
        return total != null ? total : BigDecimal.ZERO;
    }

    @Transactional(readOnly = true)
    public boolean validateAccountingEquation() {
        BigDecimal assets = getTotalBalanceByAccountType(AccountType.ASSET);
        BigDecimal liabilities = getTotalBalanceByAccountType(AccountType.LIABILITY);
        BigDecimal equity = getTotalBalanceByAccountType(AccountType.EQUITY);
        
        // Assets = Liabilities + Equity
        return assets.compareTo(liabilities.add(equity)) == 0;
    }

    @Transactional(readOnly = true)
    public List<Account> getAccountsForBalanceSheet() {
        return accountRepository.findAccountsByTypes(
            List.of(AccountType.ASSET, AccountType.LIABILITY, AccountType.EQUITY)
        );
    }

    @Transactional(readOnly = true)
    public List<Account> getAccountsForIncomeStatement() {
        return accountRepository.findAccountsByTypes(
            List.of(AccountType.REVENUE, AccountType.EXPENSE)
        );
    }
}
