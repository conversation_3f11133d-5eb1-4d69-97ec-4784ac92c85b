package org.example.ledgerbased.dto;

import org.example.ledgerbased.model.AccountType;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.math.BigDecimal;

public class CreateAccountRequest {
    
    @NotBlank(message = "Account number is required")
    @Pattern(regexp = "^[A-Z0-9-]{3,20}$", message = "Account number must be 3-20 characters, alphanumeric with hyphens")
    private String accountNumber;
    
    @NotBlank(message = "Account name is required")
    private String accountName;
    
    @NotNull(message = "Account type is required")
    private AccountType accountType;
    
    private Long parentAccountId;
    
    @NotNull(message = "Currency ID is required")
    private Long currencyId;
    
    private BigDecimal openingBalance = BigDecimal.ZERO;

    public CreateAccountRequest() {}

    public CreateAccountRequest(String accountNumber, String accountName, AccountType accountType,
                               Long currencyId, BigDecimal openingBalance) {
        this.accountNumber = accountNumber;
        this.accountName = accountName;
        this.accountType = accountType;
        this.currencyId = currencyId;
        this.openingBalance = openingBalance;
    }

    // Getters and Setters
    public String getAccountNumber() { return accountNumber; }
    public void setAccountNumber(String accountNumber) { this.accountNumber = accountNumber; }

    public String getAccountName() { return accountName; }
    public void setAccountName(String accountName) { this.accountName = accountName; }

    public AccountType getAccountType() { return accountType; }
    public void setAccountType(AccountType accountType) { this.accountType = accountType; }

    public Long getParentAccountId() { return parentAccountId; }
    public void setParentAccountId(Long parentAccountId) { this.parentAccountId = parentAccountId; }

    public Long getCurrencyId() { return currencyId; }
    public void setCurrencyId(Long currencyId) { this.currencyId = currencyId; }

    public BigDecimal getOpeningBalance() { return openingBalance; }
    public void setOpeningBalance(BigDecimal openingBalance) { this.openingBalance = openingBalance; }
}
