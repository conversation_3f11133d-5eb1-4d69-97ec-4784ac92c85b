package org.example.ledgerbased.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;

public class CreateTransactionRequest {
    
    @NotBlank(message = "Description is required")
    @Size(max = 500, message = "Description cannot exceed 500 characters")
    private String description;
    
    private String referenceNumber;
    
    @NotNull(message = "Transaction date is required")
    private LocalDate transactionDate;
    
    @NotNull(message = "Currency ID is required")
    private Long currencyId;
    
    @NotEmpty(message = "Transaction must have at least one entry")
    @Size(min = 2, message = "Transaction must have at least 2 entries for double-entry bookkeeping")
    @Valid
    private List<TransactionEntryRequest> entries;

    public CreateTransactionRequest() {}

    public CreateTransactionRequest(String description, LocalDate transactionDate, Long currencyId,
                                   List<TransactionEntryRequest> entries) {
        this.description = description;
        this.transactionDate = transactionDate;
        this.currencyId = currencyId;
        this.entries = entries;
    }

    // Getters and Setters
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getReferenceNumber() { return referenceNumber; }
    public void setReferenceNumber(String referenceNumber) { this.referenceNumber = referenceNumber; }

    public LocalDate getTransactionDate() { return transactionDate; }
    public void setTransactionDate(LocalDate transactionDate) { this.transactionDate = transactionDate; }

    public Long getCurrencyId() { return currencyId; }
    public void setCurrencyId(Long currencyId) { this.currencyId = currencyId; }

    public List<TransactionEntryRequest> getEntries() { return entries; }
    public void setEntries(List<TransactionEntryRequest> entries) { this.entries = entries; }
}
